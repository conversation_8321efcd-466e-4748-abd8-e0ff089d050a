"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import {
  Search,
  MapPin,
  Filter,
  Star,
  Heart,
  MessageSquare,
  Home,
  User,
  Bell,
  Settings,
  SlidersHorizontal,
  ChevronDown,
  TrendingUp,
  Eye,
} from "lucide-react";
import { VendorDetailModal } from "@/components/blocks/VendorDetailModel";
import { displayVendors } from "@/constants";
import { useRouter } from "next/navigation";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { Phone, MessageCircle } from "lucide-react";

export default function CustomerDashboard() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("New York, NY");
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    category: "all",
    rating: [0],
    priceRange: [0, 1000],
    location: "all",
    verified: false,
    availability: "all",
  });

  // Mock data
  const popularCategories = [
    { name: "Restaurants", icon: "🍽️", count: 1250 },
    { name: "Healthcare", icon: "🏥", count: 890 },
    { name: "Services", icon: "🔧", count: 2340 },
    { name: "Retail", icon: "🛍️", count: 1890 },
    { name: "Technology", icon: "💻", count: 560 },
    { name: "Construction", icon: "🏗️", count: 780 },
  ];

  const trendingVendors = [
    {
      id: 1,
      name: "Giuseppe's Italian Kitchen",
      category: "Restaurants",
      rating: 4.8,
      reviews: 234,
      location: "Downtown, NY",
      image: "🍝",
      isVerified: true,
    },
    {
      id: 2,
      name: "TechFix Solutions",
      category: "Technology",
      rating: 4.9,
      reviews: 156,
      location: "Midtown, NY",
      image: "💻",
      isVerified: true,
    },
    {
      id: 3,
      name: "Elite Fitness Center",
      category: "Health & Fitness",
      rating: 4.7,
      reviews: 189,
      location: "Brooklyn, NY",
      image: "💪",
      isVerified: true,
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Main Content */}
      <main className="container mx-auto p-6">
        <div className="space-y-6">
          {/* Hero Search Section */}
          <div className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-6 text-white">
            <h1 className="text-2xl font-bold mb-2">
              Find Your Perfect Vendor
            </h1>
            <p className="text-white/80 mb-6">
              Discover trusted businesses and services in your area
            </p>

            <div className="space-y-4">
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white w-5 h-5" />
                  <Input
                    placeholder="Search for vendors, services..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 h-12 rounded-xl bg-white text-white"
                  />
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  className="h-12 px-4 rounded-xl bg-white text-primary hover:bg-white/90"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="w-4 h-4" />
                </Button>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <MapPin className="w-4 h-4" />
                <span>{selectedLocation}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 text-white hover:bg-white/20"
                >
                  Change
                </Button>
              </div>
            </div>
          </div>

          {/* Popular Categories */}
          <div className="space-y-4">
            <h2 className="text-xl font-bold">Popular Categories</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {popularCategories.map((category) => (
                <Card
                  key={category.name}
                  className="p-4 hover:shadow-md transition-shadow cursor-pointer rounded-2xl"
                >
                  <div className="text-center space-y-2">
                    <div className="text-2xl">{category.icon}</div>
                    <div className="font-medium text-sm">{category.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {category.count} vendors
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Trending Vendors */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold">Trending Near You</h2>
              <Button variant="ghost" size="sm" className="text-primary">
                View All
              </Button>
            </div>
            <div className="grid gap-4">
              {displayVendors.slice(0, 5).map((v) => (
                <Card key={v.id} className="p-4 md:p-5 rounded-2xl shadow-sm">
                  <div className="grid grid-cols-1 sm:grid-cols-[160px_1fr] gap-4">
                    {/* Image */}
                    <div className="rounded-xl overflow-hidden border bg-card">
                      <div className="aspect-[4/4]">
                        <ImageWithFallback
                          src={v.image}
                          alt={v.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex flex-col justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center flex-wrap gap-3">
                          <h3 className="text-lg md:text-xl font-semibold text-foreground">
                            {v.name}
                          </h3>
                          <Badge className="bg-primary/15 text-primary border-primary/20 rounded-lg">
                            <span className="flex items-center gap-1">
                              <Star className="w-4 h-4 fill-primary text-primary" />{" "}
                              {v.rating}
                            </span>
                          </Badge>
                          {v.trending && (
                            <Badge variant="secondary" className="rounded-lg">
                              <TrendingUp className="w-4 h-4 mr-1" /> Trending
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-2 text-muted-foreground">
                          <MapPin className="w-4 h-4" />
                          <span>{v.address}</span>
                          <span className="mx-1">•</span>
                          <span>{v.distance}</span>
                        </div>

                        <div className="flex flex-wrap gap-2 pt-1">
                          {v.categories.map((c) => (
                            <Badge
                              key={c}
                              variant="secondary"
                              className="rounded-lg"
                            >
                              {c}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="mt-4 flex flex-wrap gap-3">
                        <Button variant="outline" className="rounded-xl">
                          <Phone className="w-4 h-4 mr-2" /> {v.phone}
                        </Button>
                        <Button
                          onClick={() => setSelectedVendor(v)}
                          className="rounded-xl bg-primary hover:bg-primary/90"
                        >
                          <Eye className="w-4 h-4 ml-2" />
                          View Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </main>

      {/* Vendor Detail Modal */}
      {selectedVendor && (
        <VendorDetailModal
          vendor={selectedVendor}
          onClose={() => setSelectedVendor(null)}
        />
      )}
    </div>
  );
}
