import Footer from "@/components/marketing/Footer";
import Navbar from "@/components/marketing/Navbar";
import { CartProvider } from "@/contexts/CartContext";

const navigationItems = [
  { label: "Home", href: "/" },
  // { label: "Search", href: "/search" },
  { label: "Categories", href: "/categories" },
  { label: "About Us", href: "/about-us" },
  { label: "Contact", href: "/contact" },
];

export default function WithoutLoginLayout({ children }) {
  return (
    <CartProvider>
      <Navbar navigationItems={navigationItems} />
      <div className="mt-18 pb-6">{children}</div>
      <Footer />
    </CartProvider>
  );
}
