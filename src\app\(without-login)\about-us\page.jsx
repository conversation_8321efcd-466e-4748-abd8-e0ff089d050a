"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Navbar from "@/components/marketing/Navbar";
import Hero from "@/components/marketing/Hero";
import Features from "@/components/marketing/Features";
import Testimonials from "@/components/marketing/Testimonials";
import Pricing from "@/components/marketing/Pricing";
import FAQ from "@/components/marketing/FAQ";
import Contact from "@/components/marketing/Contact";
import Footer from "@/components/marketing/Footer";
import { Users, Shield, BarChart3, Zap, Target, Globe } from "lucide-react";

export default function LandingPage() {
  const [currentSection, setCurrentSection] = useState("home");

  const router = useRouter();

  const onGetStarted = () => {
    router.push("/register");
  };

  const scrollToSection = (sectionId) => {
    // In a real app, you'd scroll to the section
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const features = [
    {
      icon: Users,
      title: "Multi-Role Platform",
      description:
        "Seamlessly manage vendors, customers, and sales teams in one unified platform.",
    },
    {
      icon: Shield,
      title: "Verified Vendors",
      description:
        "All vendors are thoroughly verified and approved by our admin team.",
    },
    {
      icon: BarChart3,
      title: "Advanced Analytics",
      description:
        "Get detailed insights and reports to make data-driven business decisions.",
    },
    {
      icon: Zap,
      title: "Instant Enquiries",
      description:
        "Connect customers with vendors instantly through our smart enquiry system.",
    },
    {
      icon: Target,
      title: "Sales Management",
      description:
        "Track sales performance and commission with our comprehensive tools.",
    },
    {
      icon: Globe,
      title: "Multi-Location",
      description:
        "Manage vendors and customers across multiple cities and regions.",
    },
  ];

  const stats = [
    { number: "10K+", label: "Active Vendors" },
    { number: "50K+", label: "Happy Customers" },
    { number: "500+", label: "Cities Covered" },
    { number: "99.9%", label: "Uptime" },
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Restaurant Owner",
      content:
        "VendorHub transformed how we connect with customers. Our enquiries increased by 300%!",
      rating: 5,
    },
    {
      name: "Mike Chen",
      role: "Sales Executive",
      content:
        "The commission tracking and vendor onboarding tools are game-changers for our sales team.",
      rating: 5,
    },
    {
      name: "Lisa Rodriguez",
      role: "Admin Manager",
      content:
        "Managing thousands of vendors has never been easier. The admin dashboard is incredibly powerful.",
      rating: 5,
    },
  ];

  const pricingPlans = [
    {
      name: "Vendor Basic",
      price: "$99",
      period: "one-time",
      description: "Perfect for small businesses getting started",
      features: [
        "Business profile listing",
        "Customer enquiry management",
        "Basic analytics",
        "Email support",
      ],
      popular: false,
    },
    {
      name: "Vendor Pro",
      price: "$199",
      period: "yearly",
      description: "Advanced features for growing businesses",
      features: [
        "Everything in Basic",
        "Advanced analytics",
        "Priority support",
        "Featured listings",
        "Custom branding",
      ],
      popular: true,
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "contact us",
      description: "Tailored solutions for large organizations",
      features: [
        "Everything in Pro",
        "API access",
        "Custom integrations",
        "Dedicated support",
        "White-label solution",
      ],
      popular: false,
    },
  ];

  const faqs = [
    {
      question: "How do I get started as a vendor?",
      answer:
        "Simply click 'Get Started', create your account, complete the business verification process, and pay the one-time registration fee. Our team will review and approve your profile within 24-48 hours.",
    },
    {
      question: "What payment methods do you accept?",
      answer:
        "We accept all major credit cards, debit cards, and digital payment methods including PayPal, Stripe, and bank transfers.",
    },
    {
      question: "How do customers find my business?",
      answer:
        "Customers can discover your business through our search functionality, category browsing, location-based results, and featured listings. We also optimize your profile for maximum visibility.",
    },
    {
      question: "What commission do sales executives earn?",
      answer:
        "Sales executives earn a competitive 50% commission on every successful vendor onboarding. Payments are processed monthly with detailed tracking available in the dashboard.",
    },
    {
      question: "Is there customer support available?",
      answer:
        "Yes! We provide 24/7 customer support through email, live chat, and phone. Enterprise customers get dedicated account managers.",
    },
    {
      question: "Can I cancel my subscription anytime?",
      answer:
        "Yes, you can cancel your subscription anytime from your dashboard. For one-time vendor registrations, refunds are available within 30 days if you haven't received any enquiries.",
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <Hero
        onGetStarted={onGetStarted}
        onLearnMore={() => scrollToSection("about")}
        stats={stats}
      />
      <Features features={features} />
      <Testimonials testimonials={testimonials} />
      <Pricing pricingPlans={pricingPlans} onGetStarted={onGetStarted} />
      <FAQ faqs={faqs} />
      <Contact />
      <Footer />
    </div>
  );
}
