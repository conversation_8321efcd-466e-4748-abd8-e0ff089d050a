"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import {
  Star,
  TrendingUp,
  MapPin,
  Phone,
  MessageCircle,
  Search,
  CheckCircle,
  Eye,
  Grid3X3,
  List,
} from "lucide-react";
import { displayVendors } from "@/constants";
import { useRouter } from "next/navigation";
import { VendorDetailModal } from "@/components/blocks/VendorDetailModel";
import { Input } from "@/components/ui/input";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";

export default function VendorsListingPage() {
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [viewMode, setViewMode] = useState("grid"); // grid or list
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    topRated: false,
    verified: false,
  });

  const filteredVendors = displayVendors.filter((v) => {
    const matchesSearch =
      v.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      v.categories.some((c) =>
        c.toLowerCase().includes(searchQuery.toLowerCase())
      );

    if (filters.topRated && v.rating < 4.5) return false;
    if (filters.verified && !v.verified) return false;
    if (searchQuery && !matchesSearch) return false;

    return true;
  });

  const router = useRouter();

  const handlePushLogin = () => {
    router.push("/login");
  };

  return (
    <main className="min-h-screen bg-background pt-2">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Heading */}
        <div className="mb-8 text-sm text-muted-foreground">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/categories/electronics">
                  Electronics
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Hardware Shops</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <h1 className="text-2xl md:text-3xl font-semibold mb-4">
          Popular Hardware Shops in Thane
        </h1>

        {/* Toolbar */}
        <div className="flex items-center justify-between gap-8 mb-6">
          <div className="flex items-center gap-3">
            <Button
              variant={filters.topRated ? "default" : "outline"}
              className="rounded-xl"
              onClick={() =>
                setFilters((prev) => ({ ...prev, topRated: !prev.topRated }))
              }
            >
              <Star className="w-4 h-4 mr-2" /> Top Rated
            </Button>

            <Button
              variant={filters.verified ? "default" : "outline"}
              className="rounded-xl"
              onClick={() =>
                setFilters((prev) => ({ ...prev, verified: !prev.verified }))
              }
            >
              <CheckCircle className="w-4 h-4 mr-2" /> Verified
            </Button>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl w-full relative">
            <div className="flex items-center bg-transparent dark:bg-input/30 dark:hover:bg-input/50 border border-input rounded-full shadow-lg">
              <Search className="w-5 h-5 text-muted-foreground ml-4" />
              <Input
                placeholder="Search for vendors or categories..."
                className="bg-transparent! dark:bg-input/30 dark:hover:bg-input/50 flex-1 border-0! focus:ring-0!"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center border rounded-lg">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="rounded-r-none"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="rounded-l-none"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <Separator />

        {/* Vendor Cards */}
        {viewMode === "grid" ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-12">
            {filteredVendors.map((v) => (
              <GridViewCard
                key={v.id}
                vendor={v}
                handlePushLogin={handlePushLogin}
                setSelectedVendor={setSelectedVendor}
              />
            ))}
          </div>
        ) : (
          <div className="space-y-6 pt-12">
            {filteredVendors.map((v) => (
              <ListViewCard
                key={v.id}
                vendor={v}
                handlePushLogin={handlePushLogin}
                setSelectedVendor={setSelectedVendor}
              />
            ))}
          </div>
        )}
      </div>

      {selectedVendor && (
        <VendorDetailModal
          vendor={selectedVendor}
          onClose={() => setSelectedVendor(null)}
        />
      )}
    </main>
  );
}

const ListViewCard = ({ vendor, handlePushLogin, setSelectedVendor }) => {
  return (
    <Card className="p-4 md:p-5 rounded-2xl shadow-sm">
      <div className="grid grid-cols-1 sm:grid-cols-[160px_1fr] gap-4">
        {/* Image */}
        <div className="rounded-xl overflow-hidden border bg-card">
          <div className="aspect-[4/4]">
            <ImageWithFallback
              src={vendor.image}
              alt={vendor.name}
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col justify-between">
          <div className="space-y-2">
            <div className="flex items-center flex-wrap gap-3">
              <h3 className="text-lg md:text-xl font-semibold text-foreground">
                {vendor.name}
              </h3>
              <Badge className="bg-primary/15 text-primary border-primary/20 rounded-lg">
                <span className="flex items-center gap-1">
                  <Star className="w-4 h-4 fill-primary text-primary" />{" "}
                  {vendor.rating}
                </span>
              </Badge>
              {vendor.trending && (
                <Badge variant="secondary" className="rounded-lg">
                  <TrendingUp className="w-4 h-4 mr-1" /> Trending
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2 text-muted-foreground">
              <MapPin className="w-4 h-4" />
              <span>{vendor.address}</span>
              <span className="mx-1">•</span>
              <span>{vendor.distance}</span>
            </div>

            <div className="flex flex-wrap gap-2 pt-1">
              {vendor.categories.map((c) => (
                <Badge key={c} variant="secondary" className="rounded-lg">
                  {c}
                </Badge>
              ))}
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-3">
            <Button
              variant="outline"
              className="rounded-xl"
              onClick={handlePushLogin}
            >
              <Phone className="w-4 h-4 mr-2" /> Contact
            </Button>
            <Button
              variant="outline"
              className="rounded-xl"
              onClick={handlePushLogin}
            >
              <MessageCircle className="w-4 h-4 mr-2" /> WhatsApp
            </Button>
            <Button
              onClick={() => setSelectedVendor(vendor)}
              className="rounded-xl bg-primary hover:bg-primary/90"
            >
              <Eye className="w-4 h-4 ml-2" />
              View Details
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export const GridViewCard = ({
  vendor,
  handlePushLogin,
  setSelectedVendor,
}) => {
  return (
    <Card className="rounded-2xl overflow-hidden shadow-sm">
      {/* Image */}
      <div className="overflow-hidden border bg-card -mt-6">
        <div className="aspect-[4/2]">
          <ImageWithFallback
            src={vendor.image}
            alt={vendor.name}
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      <CardContent className={"-pt-2"}>
        <div className="grid grid-cols-1 gap-4">
          {/* Content */}
          <div className="flex flex-col justify-between">
            <div className="space-y-2">
              <div className="flex items-center flex-wrap gap-3">
                <h3 className="text-lg md:text-xl font-semibold text-foreground">
                  {vendor.name}
                </h3>
                <Badge className="bg-primary/15 text-primary border-primary/20 rounded-lg">
                  <span className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-primary text-primary" />{" "}
                    {vendor.rating}
                  </span>
                </Badge>
              </div>

              <div className="flex items-center gap-2 text-muted-foreground">
                <MapPin className="w-4 h-4" />
                <span>{vendor.address}</span>
                <span className="mx-1">•</span>
                <span>{vendor.distance}</span>
              </div>

              <div className="flex flex-wrap gap-2 pt-1">
                {vendor.categories.map((c) => (
                  <Badge key={c} variant="secondary" className="rounded-lg">
                    {c}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="mt-4 flex flex-wrap gap-3">
              <Button
                variant="outline"
                className="rounded-xl"
                onClick={handlePushLogin}
              >
                <Phone className="w-4 h-4 mr-2" /> Contact
              </Button>
              <Button
                variant="outline"
                className="rounded-xl"
                onClick={handlePushLogin}
              >
                <MessageCircle className="w-4 h-4 mr-2" /> WhatsApp
              </Button>
              <Button
                onClick={() => setSelectedVendor(vendor)}
                className="rounded-xl bg-primary hover:bg-primary/90"
              >
                <Eye className="w-4 h-4 ml-2" />
                View Details
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
