import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { CreditCard, CheckCircle, XCircle, RotateCcw } from "lucide-react";

export default function StatsCard({ payments, setFilter }) {
  const [stats, setStats] = useState({
    totalPayments: 0,
    paidPayments: 0,
    failedPayments: 0,
    refundedPayments: 0,
    totalRevenue: 0,
  });

  useEffect(() => {
    const paidPayments = payments.filter((p) => p.status === "paid");
    const failedPayments = payments.filter((p) => p.status === "failed");
    const refundedPayments = payments.filter((p) => p.status === "refunded");
    const totalRevenue = paidPayments.reduce((sum, p) => sum + p.amount, 0);
    
    setStats({
      totalPayments: payments?.length || 0,
      paidPayments: paidPayments?.length || 0,
      failedPayments: failedPayments?.length || 0,
      refundedPayments: refundedPayments?.length || 0,
      totalRevenue: totalRevenue,
    });
  }, [payments]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CreditCard className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalPayments?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Payments</div>
          <div className="text-xs text-muted-foreground">
            {formatCurrency(stats.totalRevenue)}
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("paid")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CheckCircle className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.paidPayments.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Paid
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("failed")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <XCircle className="w-5 h-5 text-red-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.failedPayments?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Failed
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors shimmer duration-300"
        onClick={() => setFilter("refunded")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <RotateCcw className="w-5 h-5 text-orange-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.refundedPayments?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Refunded
          </div>
        </div>
      </Card>
    </div>
  );
}
