"use client";

import { Card, CardContent } from "@/components/ui/card";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { categories, subCategories } from "@/constants";
import Link from "next/link";
import React from "react";

export default function SubCategoriesPage() {
  return (
    <div>
      {/* Categories */}
      <div className="w-full flex flex-col gap-4 items-center justify-center">
        <div className="w-full bg-accent dark:bg-card py-20 flex flex-col items-center justify-center">
          <h1 className="text-4xl font-bold text-shimmer pb-2">Categories</h1>
          <p className="text-sm text-muted-foreground text-center">
            Select a category to view respective sub-categories
          </p>
        </div>
        <div className="w-full min-w-4xl max-w-6xl grid grid-cols-6 gap-4">
          {categories.map((category) => (
            <Link href={`/categories/${category.slug}`} key={category.id}>
              <Card>
                <CardContent className="flex flex-col items-center justify-center">
                  <ImageWithFallback
                    src={category.icon}
                    className="w-12 rounded-md"
                  />
                  <h3 className="font-semibold text-center">{category.name}</h3>
                  <p className="text-sm text-muted-foreground text-center">
                    {category.count} vendors
                  </p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
