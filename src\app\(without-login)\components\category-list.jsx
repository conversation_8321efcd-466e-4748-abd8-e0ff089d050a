import { categories } from "@/constants";
import React from "react";
import Link from "next/link";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { Card, CardContent } from "@/components/ui/card";

export default function CategoriesList() {
  return (
    <div className="w-full flex items-center justify-center p-6">
      <div className="w-full min-w-4xl max-w-6xl grid grid-cols-6 gap-4">
        {categories.map((category) => (
          <Link href={`/categories/${category.slug}`} key={category.id}>
            <Card>
              <CardContent className="flex flex-col items-center justify-center">
                <ImageWithFallback
                  src={category.icon}
                  className="w-12 rounded-md"
                />
                <h3 className="font-semibold text-center">{category.name}</h3>
                <p className="text-sm text-muted-foreground text-center">
                  {category.count} vendors
                </p>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  );
}
