import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building2 } from "lucide-react";
import Link from "next/link";

export default function NavBar() {
  return (
    <nav className="px-6 py-4 border-b bg-card/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="max-w-6xl mx-auto flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Building2 className="w-5 h-5 text-primary-foreground" />
          </div>
          <span className="font-bold text-xl">VendorHub</span>
        </div>
        <div className="flex items-center gap-4">
          <Link href="/login">
            <Button variant="ghost" className="rounded-xl">
              Already a member? Sign In
            </Button>
          </Link>
          <Link href="/register">
            <Button className="rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground">
              Get Started
            </Button>
          </Link>
        </div>
      </div>
    </nav>
  );
}
