import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { CircleX, Users, CircleCheckBig } from "lucide-react";

export default function StatsCard({ customers, setFilter }) {
  const [stats, setStats] = useState({
    totalCustomers: 0,
    totalVerifiedCustomers: 0,
    totalPendingCustomers: 0,
  });

  useEffect(() => {
    const verifiedCustomers = customers.filter((c) => c.verified);
    const pendingCustomers = customers.filter((c) => !c.verified);
    setStats({
      totalCustomers: customers?.length || 0,
      totalVerifiedCustomers: verifiedCustomers?.length || 0,
      totalPendingCustomers: pendingCustomers?.length || 0,
    });
  }, [customers]);

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Users className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalCustomers?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Customers</div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("verified")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CircleCheckBig className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalVerifiedCustomers.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Verified Customers
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors shimmer duration-300"
        onClick={() => setFilter("pending")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CircleX className="w-5 h-5 text-destructive" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalPendingCustomers?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Pending Verification
          </div>
        </div>
      </Card>
    </div>
  );
}
