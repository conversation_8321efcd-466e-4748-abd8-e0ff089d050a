import { Card } from "@/components/ui/card";
import { Store, Users, BarChart3, Star } from "lucide-react";

const features = [
  {
    icon: Store,
    title: "Business Profile",
    description:
      "Create a comprehensive business profile with photos, services, and contact information",
  },
  {
    icon: Users,
    title: "Customer Management",
    description: "Manage customer inquiries, bookings, and communications in one place",
  },
  {
    icon: BarChart3,
    title: "Analytics Dashboard",
    description: "Track your business performance with detailed analytics and insights",
  },
  {
    icon: Star,
    title: "Reviews & Ratings",
    description: "Build your reputation with customer reviews and ratings system",
  },
];

export default function FeaturesSection() {
  return (
    <section className="px-6 py-16 bg-card/50 backdrop-blur-sm">
      <div className="max-w-6xl mx-auto">
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-3xl font-bold">Everything You Need to Succeed</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Our platform provides all the tools and features you need to manage and grow your business effectively.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="p-6 rounded-2xl border shadow-sm hover:shadow-md transition-shadow">
              <div className="space-y-4">
                <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center">
                  <feature.icon className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-semibold mb-2">{feature.title}</h3>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

