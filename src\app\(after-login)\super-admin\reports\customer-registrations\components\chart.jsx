import React from "react";
import {
  Cartesian<PERSON>rid,
  <PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Database } from "lucide-react";

const chartConfig = {
  verified: {
    label: "Verified",
    color: "var(--chart-2)",
  },
  pending: {
    label: "Pending",
    color: "var(--chart-5)",
  },
};

export function GrowthChart({ chartData, dateFilter }) {
  return (
    <Card className={"my-4"}>
      <CardHeader>
        <CardTitle>Customer Registration Growth</CardTitle>
        <CardDescription>
          Showing customer registrations for the {dateFilter.preset}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {chartData?.length > 0 ? (
          <ChartContainer
            config={chartConfig}
            style={{ width: "100%", height: 250 }}
          >
            <ResponsiveContainer width="100%" height={250}>
              <LineChart
                accessibilityLayer
                data={chartData}
                margin={{
                  left: 24,
                  right: 12,
                  top: 10,
                  bottom: 10,
                }}
              >
                <CartesianGrid />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                />
                <YAxis tickLine={false} axisLine={false} tickMargin={8} />

                <ChartTooltip
                  cursor={false}
                  content={<ChartTooltipContent hideLabel />}
                />
                <Line
                  dataKey="verified"
                  type="bump"
                  stroke="var(--chart-2)"
                  dot={false}
                  strokeWidth={2}
                  filter="url(#rainbow-line-glow)"
                />
                <Line
                  dataKey="pending"
                  type="bump"
                  stroke="var(--chart-5)"
                  dot={false}
                  strokeWidth={2}
                  filter="url(#rainbow-line-glow)"
                />
                <defs>
                  <filter
                    id="rainbow-line-glow"
                    x="-20%"
                    y="-20%"
                    width="140%"
                    height="140%"
                  >
                    <feGaussianBlur stdDeviation="10" result="blur" />
                    <feComposite
                      in="SourceGraphic"
                      in2="blur"
                      operator="over"
                    />
                  </filter>
                </defs>
              </LineChart>
            </ResponsiveContainer>
          </ChartContainer>
        ) : (
          <div className="flex justify-center items-center h-60">
            <div className="flex flex-col items-center justify-center">
              <Database className="w-20 h-20 mx-auto mb-4 opacity-50" />
              <CardTitle>Oops! Data not Found</CardTitle>
              <CardDescription>
                No data found, try changing the date filter.
              </CardDescription>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
