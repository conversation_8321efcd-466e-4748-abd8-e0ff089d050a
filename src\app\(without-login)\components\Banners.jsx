import * as React from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ImageWithFallback } from "@/components/ui/image-fallback";

export default function Banners() {
  return (
    <Carousel className="w-full max-w-5xl max-h-[400px]">
      <CarouselContent>
        {Array.from({ length: 5 }).map((_, index) => (
          <CarouselItem key={index}>
            <div className="p-1">
              <Card className="relative flex aspect-[4/1] items-center justify-center -p-2 overflow-hidden rounded-md">
                <ImageWithFallback
                  src={`/slides/${index + 1}.JPG`}
                  className="w-full h-full object-cover"
                />
              </Card>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious />
      <CarouselNext />
    </Carousel>
  );
}
