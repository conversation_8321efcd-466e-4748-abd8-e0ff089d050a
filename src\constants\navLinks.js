import {
  AudioWaveform,
  BadgeIndianRupee,
  BookOpen,
  Bot,
  ChartColumn,
  Command,
  Frame,
  GalleryVerticalEnd,
  Map,
  PieChart,
  ReceiptText,
  Settings2,
  SquareTerminal,
  Users,
  UserCheck,
  Building,
  FileText,
  TrendingUp,
  Shield,
  Key,
  Bell,
  Database,
  Activity,
  HomeIcon,
  Star,
} from "lucide-react";

// Navigation configuration for different user roles
export const navigationConfig = {
  "super-admin": {
    user: {
      role: "Super Admin",
      name: "Super Admin",
      email: "<EMAIL>",
      avatar: "/avatars/admin.jpg",
    },
    navMain: [
      {
        title: "Dashboard",
        url: "/super-admin/dashboard",
        icon: ChartColumn,
        hideDropdown: true,
      },
      {
        title: "Management",
        url: "#",
        icon: SquareTerminal,
        items: [
          {
            title: "Plans",
            url: "/super-admin/management/plans",
          },
          {
            title: "Categories",
            url: "/super-admin/management/categories",
          },
          {
            title: "Sub-Categories",
            url: "/super-admin/management/sub-categories",
          },
          {
            title: "Users",
            url: "/super-admin/management/users",
          },
          {
            title: "Audit Logs",
            url: "/super-admin/management/audit-logs",
          },
          {
            title: "Enquiries",
            url: "/super-admin/management/enquiries",
          },
        ],
      },
      {
        title: "Sales",
        url: "#",
        icon: BadgeIndianRupee,
        items: [
          {
            title: "Commissions Distribution",
            url: "/super-admin/sales/commissions",
          },
          {
            title: "Field Visits",
            url: "/super-admin/sales/field-visits",
          },
          {
            title: "Payments History",
            url: "/super-admin/sales/payments-history",
          },
        ],
      },
      {
        title: "Reports",
        url: "#",
        icon: ReceiptText,
        items: [
          {
            title: "Vendors Onboarding",
            url: "/super-admin/reports/vendors-onboarding",
          },
          {
            title: "Customer Registrations",
            url: "/super-admin/reports/customer-registrations",
          },
          {
            title: "Enquiries",
            url: "/super-admin/reports/enquiries",
          },
          {
            title: "Engagement",
            url: "/super-admin/reports/engagement",
          },
          {
            title: "Plans & Payments",
            url: "/super-admin/reports/plans-payments",
          },
        ],
      },
      {
        title: "Settings",
        url: "#",
        icon: Settings2,
        items: [
          {
            title: "API Keys",
            url: "/super-admin/settings/api-keys",
          },
        ],
      },
    ],
  },

  // Admin role configuration
  admin: {
    user: {
      role: "Admin",
      name: "Admin",
      email: "<EMAIL>",
      avatar: "/avatars/admin.jpg",
    },
    navMain: [
      {
        title: "Dashboard",
        url: "/admin/dashboard",
        icon: ChartColumn,
        hideDropdown: true,
      },
      {
        title: "User Management",
        url: "#",
        icon: Users,
        items: [
          {
            title: "Vendors",
            url: "/admin/users/vendors",
          },
          {
            title: "Customers",
            url: "/admin/users/customers",
          },
          {
            title: "Sales Executives",
            url: "/admin/users/sales-executives",
          },
        ],
      },
      {
        title: "Content Management",
        url: "#",
        icon: FileText,
        items: [
          {
            title: "Categories",
            url: "/admin/content/categories",
          },
          {
            title: "Sub-Categories",
            url: "/admin/content/sub-categories",
          },
        ],
      },
      {
        title: "Reports",
        url: "#",
        icon: ReceiptText,
        items: [
          {
            title: "User Activity",
            url: "/admin/reports/user-activity",
          },
          {
            title: "Revenue",
            url: "/admin/reports/revenue",
          },
        ],
      },
    ],
  },

  // Vendor role configuration
  vendor: {
    user: {
      role: "Vendor",
      name: "Vendor",
      email: "<EMAIL>",
      avatar: "/avatars/vendor.jpg",
    },
    navMain: [
      {
        title: "Dashboard",
        url: "/vendor/dashboard",
        icon: ChartColumn,
        hideDropdown: true,
      },
      {
        title: "Enquiries",
        url: "/vendor/enquiries",
        icon: Bell,
        hideDropdown: true,
      },
      {
        title: "Payments",
        url: "#",
        icon: BadgeIndianRupee,
        items: [
          {
            title: "Payment History",
            url: "/vendor/payments/history",
          },
          {
            title: "Subscription",
            url: "/vendor/payments/subscription",
          },
        ],
      },
      {
        title: "Profile",
        url: "/vendor/profile",
        icon: UserCheck,
        hideDropdown: true,
      },
    ],
  },

  // Customer role configuration
  customer: {
    user: {
      role: "Customer",
      name: "Customer",
      email: "<EMAIL>",
      avatar: "/avatars/customer.jpg",
    },
    navMain: [
      {
        title: "Home",
        url: "/customer/home",
        icon: HomeIcon,
        hideDropdown: true,
      },
      {
        title: "My Enquiries",
        url: "/customer/enquiries",
        icon: Bell,
        hideDropdown: true,
      },
      {
        title: "Favorites",
        url: "/customer/favorites",
        icon: Star,
        hideDropdown: true,
      },
      {
        title: "Profile",
        url: "/customer/profile",
        icon: UserCheck,
        hideDropdown: true,
      },
    ],
  },

  // Sales Executive role configuration
  "sales-executive": {
    user: {
      role: "Sales Executive",
      name: "Sales Executive",
      email: "<EMAIL>",
      avatar: "/avatars/sales.jpg",
    },
    navMain: [
      {
        title: "Dashboard",
        url: "/sales/dashboard",
        icon: ChartColumn,
        hideDropdown: true,
      },
      {
        title: "Field Visits",
        url: "/sales/visits",
        icon: Map,
        hideDropdown: true,
      },
      {
        title: "Vendor Profiles",
        url: "/sales/vendors",
        icon: TrendingUp,
        hideDropdown: true,
      },
    ],
  },
};

// Helper function to get navigation data for a specific role
export const getNavigationForRole = (role) => {
  return navigationConfig[role] || navigationConfig["customer"]; // Default to customer if role not found
};

// Helper function to get all available roles
export const getAvailableRoles = () => {
  return Object.keys(navigationConfig);
};
