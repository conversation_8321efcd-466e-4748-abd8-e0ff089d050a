"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Building2 } from "lucide-react";
import Link from "next/link";

export default function LoginPage() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [otp, setOtp] = useState("");
  const [selectedRole, setSelectedRole] = useState("customer");
  const [isLoading, setIsLoading] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);

  const router = useRouter();

  const handleLogin = () => {
    if (!username.trim() || !password.trim()) return;

    setIsLoading(true);

    // Simulate login success after 1 second
    setTimeout(() => {
      setIsLoading(false);
      setShowOtpInput(true); // Now show OTP input
      console.log("Logged in successfully, now wait for OTP");
    }, 1000);
  };

  const handleVerifyOTP = () => {
    if (!otp.trim()) return;

    const roleRedirects = {
      customer: "/customer/home",
      vendor: "/vendor/dashboard",
      sales_executive: "/sales/dashboard",
      admin: "/admin/dashboard",
      super_admin: "/super-admin/dashboard",
    };

    const redirectPath = roleRedirects[selectedRole];
    if (redirectPath) {
      router.push(redirectPath);
    } else {
      alert("Invalid role selected");
    }
  };

  const roleOptions = [
    {
      value: "customer",
      label: "Customer",
      description: "Find and connect with vendors",
    },
    {
      value: "vendor",
      label: "Vendor",
      description: "List your business and services",
    },
    {
      value: "sales_executive",
      label: "Sales Executive",
      description: "Manage vendor onboarding",
    },
    { value: "admin", label: "Admin", description: "Platform administration" },
    {
      value: "super_admin",
      label: "Super Admin",
      description: "Full system access",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-accent/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary rounded-2xl flex items-center justify-center shadow-lg">
            <Building2 className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">Welcome back</h1>
            <p className="text-muted-foreground">Sign in to your account</p>
          </div>
        </div>

        {/* Login Form */}
        <Card className="p-6 rounded-2xl border shadow-lg">
          <div className="space-y-6">
            {/* Username Input */}
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                type="text"
                placeholder="your_username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="rounded-xl h-12"
                disabled={showOtpInput}
              />
            </div>

            {/* Password Input */}
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="rounded-xl h-12"
                disabled={showOtpInput}
              />
            </div>

            {/* Role Selector */}
            <div className="space-y-2">
              <Label htmlFor="role">I am a</Label>
              <Select
                value={selectedRole}
                onValueChange={(value) => setSelectedRole(value)}
                disabled={showOtpInput}
              >
                <SelectTrigger className="rounded-xl w-full h-12! text-left">
                  <SelectValue placeholder="Select your role" />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-sm text-muted-foreground">
                          {option.description}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* OTP Input */}
            {showOtpInput && (
              <div className="space-y-2">
                <Label htmlFor="otp">Enter OTP</Label>
                <Input
                  id="otp"
                  type="text"
                  placeholder="123456"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  className="rounded-xl h-12"
                />
              </div>
            )}

            {/* Action Button */}
            {!showOtpInput ? (
              <Button
                onClick={handleLogin}
                disabled={!username.trim() || !password.trim() || isLoading}
                className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
              >
                {isLoading ? "Logging in..." : "Login"}
              </Button>
            ) : (
              <Button
                onClick={handleVerifyOTP}
                disabled={!otp.trim()}
                className="w-full h-12 rounded-xl bg-primary hover:bg-primary/90 text-white"
              >
                Verify OTP
              </Button>
            )}

            {/* Register Link */}
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                New here?{" "}
                <Link
                  href={"/register"}
                  className="text-primary hover:underline font-medium"
                >
                  Create an account
                </Link>
              </p>
            </div>
          </div>
        </Card>

        {/* Info Card */}
        <Card className="p-4 rounded-2xl border bg-muted/30">
          <p className="text-xs text-muted-foreground text-center">
            Demo: Use any username, password & OTP. Role decides the redirect
            path.
          </p>
        </Card>
      </div>
    </div>
  );
}
