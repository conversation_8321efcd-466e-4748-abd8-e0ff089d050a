"use client";

import { Card, CardContent } from "@/components/ui/card";
import { ImageWithFallback } from "@/components/ui/image-fallback";
import { categories, subCategories } from "@/constants";
import Link from "next/link";
import { useParams } from "next/navigation";
import React from "react";

export default function SubCategoriesPage() {
  const params = useParams();
  const { slug } = params;
  return (
    <div>
      {/* Categories */}
      <div className="w-full flex flex-col gap-4 items-center justify-center">
        <div className="w-full bg-accent dark:bg-card py-20 flex flex-col items-center justify-center">
          <h1 className="text-4xl font-bold text-shimmer pb-2">
            {categories.find((c) => c.slug === slug).name}
          </h1>
          <p className="text-sm text-muted-foreground text-center">
            Select a sub-category to view vendors
          </p>
        </div>
        <div className="w-full min-w-4xl max-w-6xl grid grid-cols-3 gap-4">
          {subCategories
            .filter((category) => category.categories.slug === slug)
            .map((category) => (
              <Link href={"/vendors/listing"} key={category.id}>
                <Card className={"rounded-md overflow-hidden "}>
                  <ImageWithFallback
                    src={category.coverImage}
                    className="w-full h-[300px] object-cover overflow-hidden -mt-6"
                  />
                  <CardContent className="flex flex-col items-center justify-center">
                    <h3 className="font-semibold text-center">
                      {category.name}
                    </h3>
                    <p className="text-sm text-muted-foreground text-center">
                      {category.count} vendors
                    </p>
                  </CardContent>
                </Card>
              </Link>
            ))}
        </div>
      </div>
    </div>
  );
}
