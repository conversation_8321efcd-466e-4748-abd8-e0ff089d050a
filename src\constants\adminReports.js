// Admin reports sample data

export const userActivityReport = {
  totals: { logins: 1240, actions: 5320, failures: 24 },
  recent: [
    { id: 1, user: "<PERSON>", action: "Approved vendor", time: "2h" },
    { id: 2, user: "<PERSON>", action: "Created enquiry", time: "4h" },
    { id: 3, user: "<PERSON>", action: "Exported report", time: "1d" },
  ],
};

export const revenueReport = {
  summary: { today: 297, thisWeek: 1386, thisMonth: 4125 },
  byPlan: [
    { plan: "Starter", count: 38, revenue: 3762 },
    { plan: "Pro", count: 8, revenue: 1592 },
    { plan: "Enterprise", count: 2, revenue: 998 },
  ],
};

export const customerRegistrationsReport = {
  weekly: [
    { day: "Mon", customers: 24 },
    { day: "Tue", customers: 31 },
    { day: "Wed", customers: 18 },
    { day: "Thu", customers: 36 },
    { day: "Fri", customers: 29 },
    { day: "Sat", customers: 14 },
    { day: "Sun", customers: 12 },
  ],
  total: 164,
};

// Enhanced customer registrations data for detailed reporting
export const displayCustomerRegistrations = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "****** 0101",
    city: "New York",
    status: "active",
    verified: true,
    registrationSource: "Website",
    users: {
      id: 1,
      username: "alice_j",
      firstName: "Alice",
      lastName: "Johnson",
    },
    created: "2025-01-15T10:30:00Z",
  },
  {
    id: 2,
    name: "Bob Smith",
    email: "<EMAIL>",
    phone: "****** 0102",
    city: "Los Angeles",
    status: "active",
    verified: true,
    registrationSource: "Mobile App",
    users: {
      id: 2,
      username: "bob_smith",
      firstName: "Bob",
      lastName: "Smith",
    },
    created: "2025-01-14T14:20:00Z",
  },
  {
    id: 3,
    name: "Carol Davis",
    email: "<EMAIL>",
    phone: "****** 0103",
    city: "Chicago",
    status: "pending",
    verified: false,
    registrationSource: "Website",
    users: {
      id: 3,
      username: "carol_d",
      firstName: "Carol",
      lastName: "Davis",
    },
    created: "2025-01-13T09:15:00Z",
  },
  {
    id: 4,
    name: "David Wilson",
    email: "<EMAIL>",
    phone: "****** 0104",
    city: "Houston",
    status: "active",
    verified: true,
    registrationSource: "Social Media",
    users: {
      id: 4,
      username: "david_w",
      firstName: "David",
      lastName: "Wilson",
    },
    created: "2025-01-12T16:45:00Z",
  },
  {
    id: 5,
    name: "Emma Brown",
    email: "<EMAIL>",
    phone: "****** 0105",
    city: "Phoenix",
    status: "active",
    verified: true,
    registrationSource: "Website",
    users: {
      id: 5,
      username: "emma_b",
      firstName: "Emma",
      lastName: "Brown",
    },
    created: "2025-01-11T11:30:00Z",
  },
  {
    id: 6,
    name: "Frank Miller",
    email: "<EMAIL>",
    phone: "****** 0106",
    city: "Philadelphia",
    status: "inactive",
    verified: false,
    registrationSource: "Mobile App",
    users: {
      id: 6,
      username: "frank_m",
      firstName: "Frank",
      lastName: "Miller",
    },
    created: "2025-01-10T13:20:00Z",
  },
  {
    id: 7,
    name: "Grace Taylor",
    email: "<EMAIL>",
    phone: "****** 0107",
    city: "San Antonio",
    status: "active",
    verified: true,
    registrationSource: "Website",
    users: {
      id: 7,
      username: "grace_t",
      firstName: "Grace",
      lastName: "Taylor",
    },
    created: "2025-01-09T08:45:00Z",
  },
  {
    id: 8,
    name: "Henry Anderson",
    email: "<EMAIL>",
    phone: "****** 0108",
    city: "San Diego",
    status: "active",
    verified: true,
    registrationSource: "Referral",
    users: {
      id: 8,
      username: "henry_a",
      firstName: "Henry",
      lastName: "Anderson",
    },
    created: "2025-01-08T15:10:00Z",
  },
  {
    id: 9,
    name: "Ivy Thomas",
    email: "<EMAIL>",
    phone: "****** 0109",
    city: "Dallas",
    status: "pending",
    verified: false,
    registrationSource: "Website",
    users: {
      id: 9,
      username: "ivy_t",
      firstName: "Ivy",
      lastName: "Thomas",
    },
    created: "2025-01-07T12:25:00Z",
  },
  {
    id: 10,
    name: "Jack Jackson",
    email: "<EMAIL>",
    phone: "****** 0110",
    city: "San Jose",
    status: "active",
    verified: true,
    registrationSource: "Mobile App",
    users: {
      id: 10,
      username: "jack_j",
      firstName: "Jack",
      lastName: "Jackson",
    },
    created: "2025-01-06T17:30:00Z",
  },
];
