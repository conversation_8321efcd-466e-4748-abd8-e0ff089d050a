// Reports-oriented sample data

export const enquiriesReport = {
  statusCounts: [
    { status: "new", count: 58 },
    { status: "responded", count: 132 },
    { status: "closed", count: 84 },
  ],
  daily: [
    { day: "Mon", enquiries: 45 },
    { day: "Tue", enquiries: 52 },
    { day: "Wed", enquiries: 38 },
    { day: "Thu", enquiries: 64 },
    { day: "Fri", enquiries: 58 },
    { day: "Sat", enquiries: 42 },
    { day: "Sun", enquiries: 35 },
  ],
  topCategories: [
    { name: "Restaurants & Food", enquiries: 120 },
    { name: "Services", enquiries: 90 },
    { name: "Technology", enquiries: 70 },
  ],
};

// Enhanced enquiries data for detailed reporting
export const displayEnquiriesData = [
  {
    id: 1,
    customerName: "<PERSON> Johnson",
    customerEmail: "<EMAIL>",
    vendorName: "Balaji Hardware and Electrical",
    category: "Hardware Shops",
    message: "Looking for electrical wiring supplies for home renovation",
    status: "new",
    priority: "medium",
    verified: true,
    users: {
      id: 1,
      username: "alice_j",
      firstName: "<PERSON>",
      lastName: "<PERSON>",
    },
    created: "2025-01-15T10:30:00Z",
  },
  {
    id: 2,
    customerName: "<PERSON>",
    customerEmail: "<EMAIL>",
    vendorName: "Giuseppe's Italian Kitchen",
    category: "Restaurants & Food",
    message: "Interested in catering services for corporate event",
    status: "responded",
    priority: "high",
    verified: true,
    users: {
      id: 2,
      username: "bob_smith",
      firstName: "Bob",
      lastName: "Smith",
    },
    created: "2025-01-14T14:20:00Z",
  },
  {
    id: 3,
    customerName: "Carol Davis",
    customerEmail: "<EMAIL>",
    vendorName: "TechFix Solutions",
    category: "Technology",
    message: "Need laptop repair services urgently",
    status: "closed",
    priority: "high",
    verified: false,
    users: {
      id: 3,
      username: "carol_d",
      firstName: "Carol",
      lastName: "Davis",
    },
    created: "2025-01-13T09:15:00Z",
  },
  {
    id: 4,
    customerName: "David Wilson",
    customerEmail: "<EMAIL>",
    vendorName: "Green Garden Landscaping",
    category: "Services",
    message: "Quote for backyard landscaping project",
    status: "new",
    priority: "low",
    verified: true,
    users: {
      id: 4,
      username: "david_w",
      firstName: "David",
      lastName: "Wilson",
    },
    created: "2025-01-12T16:45:00Z",
  },
  {
    id: 5,
    customerName: "Emma Brown",
    customerEmail: "<EMAIL>",
    vendorName: "City Plumbing Services",
    category: "Services",
    message: "Emergency plumbing repair needed",
    status: "responded",
    priority: "high",
    verified: true,
    users: {
      id: 5,
      username: "emma_b",
      firstName: "Emma",
      lastName: "Brown",
    },
    created: "2025-01-11T11:30:00Z",
  },
  {
    id: 6,
    customerName: "Frank Miller",
    customerEmail: "<EMAIL>",
    vendorName: "Metro Electronics",
    category: "Technology",
    message: "Looking for smartphone accessories",
    status: "new",
    priority: "low",
    verified: false,
    users: {
      id: 6,
      username: "frank_m",
      firstName: "Frank",
      lastName: "Miller",
    },
    created: "2025-01-10T13:20:00Z",
  },
  {
    id: 7,
    customerName: "Grace Taylor",
    customerEmail: "<EMAIL>",
    vendorName: "Sunrise Bakery",
    category: "Restaurants & Food",
    message: "Custom cake order for birthday party",
    status: "closed",
    priority: "medium",
    verified: true,
    users: {
      id: 7,
      username: "grace_t",
      firstName: "Grace",
      lastName: "Taylor",
    },
    created: "2025-01-09T08:45:00Z",
  },
  {
    id: 8,
    customerName: "Henry Anderson",
    customerEmail: "<EMAIL>",
    vendorName: "AutoFix Garage",
    category: "Services",
    message: "Car maintenance and oil change",
    status: "responded",
    priority: "medium",
    verified: true,
    users: {
      id: 8,
      username: "henry_a",
      firstName: "Henry",
      lastName: "Anderson",
    },
    created: "2025-01-08T15:10:00Z",
  },
  {
    id: 9,
    customerName: "Ivy Thomas",
    customerEmail: "<EMAIL>",
    vendorName: "Fashion Forward Boutique",
    category: "Fashion",
    message: "Inquiry about wedding dress collection",
    status: "new",
    priority: "medium",
    verified: false,
    users: {
      id: 9,
      username: "ivy_t",
      firstName: "Ivy",
      lastName: "Thomas",
    },
    created: "2025-01-07T12:25:00Z",
  },
  {
    id: 10,
    customerName: "Jack Jackson",
    customerEmail: "<EMAIL>",
    vendorName: "Home Improvement Pro",
    category: "Services",
    message: "Kitchen renovation consultation",
    status: "closed",
    priority: "high",
    verified: true,
    users: {
      id: 10,
      username: "jack_j",
      firstName: "Jack",
      lastName: "Jackson",
    },
    created: "2025-01-06T17:30:00Z",
  },
];

export const plansPaymentsReport = {
  payments: [
    {
      id: 201,
      vendor: "Giuseppe's Italian Kitchen",
      plan: "Starter",
      amount: 99,
      date: "2024-01-15",
      status: "paid",
    },
    {
      id: 202,
      vendor: "TechFix Solutions",
      plan: "Starter",
      amount: 99,
      date: "2024-01-14",
      status: "paid",
    },
    {
      id: 203,
      vendor: "Green Garden Landscaping",
      plan: "Starter",
      amount: 99,
      date: "2024-01-12",
      status: "paid",
    },
  ],
  totals: { paid: 297, failed: 0, refunded: 0 },
};

// Enhanced plans-payments data for detailed reporting
export const displayPlansPaymentsData = [
  {
    id: 1,
    vendorName: "Giuseppe's Italian Kitchen",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    status: "paid",
    paymentMethod: "Credit Card",
    transactionId: "TXN001ABC",
    verified: true,
    users: {
      id: 1,
      username: "mario_g",
      firstName: "Mario",
      lastName: "Giuseppe",
    },
    created: "2025-01-15T10:30:00Z",
  },
  {
    id: 2,
    vendorName: "TechFix Solutions",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "monthly",
    amount: 199,
    currency: "USD",
    status: "paid",
    paymentMethod: "Debit Card",
    transactionId: "TXN002DEF",
    verified: true,
    users: {
      id: 2,
      username: "john_s",
      firstName: "John",
      lastName: "Smith",
    },
    created: "2025-01-14T14:20:00Z",
  },
  {
    id: 3,
    vendorName: "Green Garden Landscaping",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    status: "failed",
    paymentMethod: "Credit Card",
    transactionId: "TXN003GHI",
    verified: false,
    users: {
      id: 3,
      username: "lisa_s",
      firstName: "Lisa",
      lastName: "Smith",
    },
    created: "2025-01-13T09:15:00Z",
  },
  {
    id: 4,
    vendorName: "City Plumbing Services",
    vendorEmail: "<EMAIL>",
    planName: "Enterprise",
    planType: "yearly",
    amount: 4990,
    currency: "USD",
    status: "paid",
    paymentMethod: "Bank Transfer",
    transactionId: "TXN004JKL",
    verified: true,
    users: {
      id: 4,
      username: "mike_j",
      firstName: "Mike",
      lastName: "Johnson",
    },
    created: "2025-01-12T16:45:00Z",
  },
  {
    id: 5,
    vendorName: "Metro Electronics",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "monthly",
    amount: 199,
    currency: "USD",
    status: "refunded",
    paymentMethod: "Credit Card",
    transactionId: "TXN005MNO",
    verified: true,
    users: {
      id: 5,
      username: "sarah_w",
      firstName: "Sarah",
      lastName: "Wilson",
    },
    created: "2025-01-11T11:30:00Z",
  },
  {
    id: 6,
    vendorName: "Sunrise Bakery",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    status: "paid",
    paymentMethod: "PayPal",
    transactionId: "TXN006PQR",
    verified: true,
    users: {
      id: 6,
      username: "anna_b",
      firstName: "Anna",
      lastName: "Brown",
    },
    created: "2025-01-10T13:20:00Z",
  },
  {
    id: 7,
    vendorName: "AutoFix Garage",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "monthly",
    amount: 199,
    currency: "USD",
    status: "failed",
    paymentMethod: "Credit Card",
    transactionId: "TXN007STU",
    verified: false,
    users: {
      id: 7,
      username: "tom_d",
      firstName: "Tom",
      lastName: "Davis",
    },
    created: "2025-01-09T08:45:00Z",
  },
  {
    id: 8,
    vendorName: "Fashion Forward Boutique",
    vendorEmail: "<EMAIL>",
    planName: "Enterprise",
    planType: "monthly",
    amount: 499,
    currency: "USD",
    status: "paid",
    paymentMethod: "Credit Card",
    transactionId: "TXN008VWX",
    verified: true,
    users: {
      id: 8,
      username: "emma_t",
      firstName: "Emma",
      lastName: "Taylor",
    },
    created: "2025-01-08T15:10:00Z",
  },
  {
    id: 9,
    vendorName: "Home Improvement Pro",
    vendorEmail: "<EMAIL>",
    planName: "Starter",
    planType: "monthly",
    amount: 99,
    currency: "USD",
    status: "paid",
    paymentMethod: "Debit Card",
    transactionId: "TXN009YZA",
    verified: true,
    users: {
      id: 9,
      username: "robert_a",
      firstName: "Robert",
      lastName: "Anderson",
    },
    created: "2025-01-07T12:25:00Z",
  },
  {
    id: 10,
    vendorName: "Digital Marketing Hub",
    vendorEmail: "<EMAIL>",
    planName: "Pro",
    planType: "yearly",
    amount: 1990,
    currency: "USD",
    status: "paid",
    paymentMethod: "Bank Transfer",
    transactionId: "TXN010BCD",
    verified: true,
    users: {
      id: 10,
      username: "jennifer_m",
      firstName: "Jennifer",
      lastName: "Miller",
    },
    created: "2025-01-06T17:30:00Z",
  },
];

export const vendorsOnboardingReport = {
  series: [
    { day: "Mon", onboarded: 4 },
    { day: "Tue", onboarded: 6 },
    { day: "Wed", onboarded: 5 },
    { day: "Thu", onboarded: 7 },
    { day: "Fri", onboarded: 8 },
    { day: "Sat", onboarded: 3 },
    { day: "Sun", onboarded: 2 },
  ],
};

export const engagementReport = {
  metrics: [
    { name: "Daily Active Users", value: 1240 },
    { name: "Weekly Active Users", value: 5320 },
    { name: "Avg. Session (min)", value: 5.6 },
  ],
  notifications: [
    { id: 1, title: "New review posted", time: "2h" },
    { id: 2, title: "Vendor responded to enquiry", time: "5h" },
  ],
};

// Enhanced engagement data for detailed reporting
export const displayEngagementData = [
  {
    id: 1,
    userId: 1,
    userName: "Alice Johnson",
    userEmail: "<EMAIL>",
    activityType: "page_view",
    page: "/vendors/search",
    sessionDuration: 8.5,
    deviceType: "desktop",
    location: "New York",
    verified: true,
    users: {
      id: 1,
      username: "alice_j",
      firstName: "Alice",
      lastName: "Johnson",
    },
    created: "2025-01-15T10:30:00Z",
  },
  {
    id: 2,
    userId: 2,
    userName: "Bob Smith",
    userEmail: "<EMAIL>",
    activityType: "enquiry_sent",
    page: "/vendors/123",
    sessionDuration: 12.3,
    deviceType: "mobile",
    location: "Los Angeles",
    verified: true,
    users: {
      id: 2,
      username: "bob_smith",
      firstName: "Bob",
      lastName: "Smith",
    },
    created: "2025-01-14T14:20:00Z",
  },
  {
    id: 3,
    userId: 3,
    userName: "Carol Davis",
    userEmail: "<EMAIL>",
    activityType: "review_posted",
    page: "/vendors/456",
    sessionDuration: 6.7,
    deviceType: "tablet",
    location: "Chicago",
    verified: false,
    users: {
      id: 3,
      username: "carol_d",
      firstName: "Carol",
      lastName: "Davis",
    },
    created: "2025-01-13T09:15:00Z",
  },
  {
    id: 4,
    userId: 4,
    userName: "David Wilson",
    userEmail: "<EMAIL>",
    activityType: "profile_update",
    page: "/profile",
    sessionDuration: 4.2,
    deviceType: "desktop",
    location: "Houston",
    verified: true,
    users: {
      id: 4,
      username: "david_w",
      firstName: "David",
      lastName: "Wilson",
    },
    created: "2025-01-12T16:45:00Z",
  },
  {
    id: 5,
    userId: 5,
    userName: "Emma Brown",
    userEmail: "<EMAIL>",
    activityType: "search_performed",
    page: "/search",
    sessionDuration: 15.8,
    deviceType: "mobile",
    location: "Phoenix",
    verified: true,
    users: {
      id: 5,
      username: "emma_b",
      firstName: "Emma",
      lastName: "Brown",
    },
    created: "2025-01-11T11:30:00Z",
  },
  {
    id: 6,
    userId: 6,
    userName: "Frank Miller",
    userEmail: "<EMAIL>",
    activityType: "page_view",
    page: "/vendors/789",
    sessionDuration: 3.1,
    deviceType: "desktop",
    location: "Philadelphia",
    verified: false,
    users: {
      id: 6,
      username: "frank_m",
      firstName: "Frank",
      lastName: "Miller",
    },
    created: "2025-01-10T13:20:00Z",
  },
  {
    id: 7,
    userId: 7,
    userName: "Grace Taylor",
    userEmail: "<EMAIL>",
    activityType: "bookmark_added",
    page: "/vendors/321",
    sessionDuration: 9.4,
    deviceType: "mobile",
    location: "San Antonio",
    verified: true,
    users: {
      id: 7,
      username: "grace_t",
      firstName: "Grace",
      lastName: "Taylor",
    },
    created: "2025-01-09T08:45:00Z",
  },
  {
    id: 8,
    userId: 8,
    userName: "Henry Anderson",
    userEmail: "<EMAIL>",
    activityType: "enquiry_sent",
    page: "/vendors/654",
    sessionDuration: 7.6,
    deviceType: "tablet",
    location: "San Diego",
    verified: true,
    users: {
      id: 8,
      username: "henry_a",
      firstName: "Henry",
      lastName: "Anderson",
    },
    created: "2025-01-08T15:10:00Z",
  },
  {
    id: 9,
    userId: 9,
    userName: "Ivy Thomas",
    userEmail: "<EMAIL>",
    activityType: "page_view",
    page: "/dashboard",
    sessionDuration: 11.2,
    deviceType: "desktop",
    location: "Dallas",
    verified: false,
    users: {
      id: 9,
      username: "ivy_t",
      firstName: "Ivy",
      lastName: "Thomas",
    },
    created: "2025-01-07T12:25:00Z",
  },
  {
    id: 10,
    userId: 10,
    userName: "Jack Jackson",
    userEmail: "<EMAIL>",
    activityType: "review_posted",
    page: "/vendors/987",
    sessionDuration: 5.9,
    deviceType: "mobile",
    location: "San Jose",
    verified: true,
    users: {
      id: 10,
      username: "jack_j",
      firstName: "Jack",
      lastName: "Jackson",
    },
    created: "2025-01-06T17:30:00Z",
  },
];
