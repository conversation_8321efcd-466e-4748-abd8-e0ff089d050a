import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { MessageSquare, Clock, CheckCircle } from "lucide-react";

export default function StatsCard({ enquiries, setFilter }) {
  const [stats, setStats] = useState({
    totalEnquiries: 0,
    newEnquiries: 0,
    respondedEnquiries: 0,
    closedEnquiries: 0,
  });

  useEffect(() => {
    const newEnquiries = enquiries.filter((e) => e.status === "new");
    const respondedEnquiries = enquiries.filter((e) => e.status === "responded");
    const closedEnquiries = enquiries.filter((e) => e.status === "closed");
    setStats({
      totalEnquiries: enquiries?.length || 0,
      newEnquiries: newEnquiries?.length || 0,
      respondedEnquiries: respondedEnquiries?.length || 0,
      closedEnquiries: closedEnquiries?.length || 0,
    });
  }, [enquiries]);

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("all")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <MessageSquare className="w-5 h-5 text-primary" />
          </div>
          <div className="text-2xl font-bold">
            {stats.totalEnquiries?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Enquiries</div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("new")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Clock className="w-5 h-5 text-orange-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.newEnquiries.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            New Enquiries
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors ease-in duration-300"
        onClick={() => setFilter("responded")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <MessageSquare className="w-5 h-5 text-blue-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.respondedEnquiries?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Responded
          </div>
        </div>
      </Card>

      <Card
        className="p-4 rounded-2xl cursor-pointer hover:border-primary! transition-colors shimmer duration-300"
        onClick={() => setFilter("closed")}
      >
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <CheckCircle className="w-5 h-5 text-green-500" />
          </div>
          <div className="text-2xl font-bold">
            {stats.closedEnquiries?.toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">
            Closed
          </div>
        </div>
      </Card>
    </div>
  );
}
